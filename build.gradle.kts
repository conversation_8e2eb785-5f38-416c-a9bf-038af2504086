plugins {
    kotlin("jvm") version "2.2.0"
    id("org.jetbrains.kotlinx.benchmark") version "0.4.10"
}

group = "oryx"
version = "1.0"

repositories {
    mavenCentral()
    maven("https://jitpack.io")
}

dependencies {
    implementation("dev.lyzev.api:piko:1.1.0")
    implementation("net.lenni0451:LambdaEvents:2.4.2")

    implementation("org.jetbrains.kotlinx:kotlinx-benchmark-runtime:0.4.10")
}

/*benchmark {
    targets {
        register("main") {
            this as org.jetbrains.kotlinx.benchmark.gradle.JvmBenchmarkTarget
        }
    }
}*/

kotlin {
    jvmToolchain(24)
}