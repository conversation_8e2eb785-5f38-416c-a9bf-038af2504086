package oryx

import net.lenni0451.lambdaevents.EventHandler
import java.lang.management.ManagementFactory

// Sự kiện mẫu
data class TestEvent(val value: Int)

@State(Scope.Benchmark)
class EventBenchmarks {

    private lateinit var pikoDispatcher: 
    private lateinit var lambdaManager: EventManager

    @Setup
    fun setup() {
        // Piko
        pikoDispatcher = EventDispatcher()
        pikoDispatcher.register<TestEvent> {
            it.value + 1
        }

        // LambdaEvents
        lambdaManager = EventManager(ByteBuddyGenerator())
        lambdaManager.register(this)
    }

    // Listener cho LambdaEvents
    @EventHandler
    fun onTest(event: TestEvent) {
        event.value + 1
    }

    // =============== BENCHMARK TỐC ĐỘ ===============
    @Benchmark
    fun benchmarkPiko() {
        repeat(1000) {
            pikoDispatcher.dispatch(TestEvent(it))
        }
    }

    @Benchmark
    fun benchmarkLambdaEvents() {
        repeat(1000) {
            lambdaManager.call(TestEvent(it))
        }
    }

    // =============== BENCHMARK MEMORY ===============
    @Benchmark
    fun memoryUsagePiko(): Long {
        val before = usedMemory()
        val dispatcher = EventDispatcher()
        repeat(10_000) {
            dispatcher.register<TestEvent> { e -> e.value + 1 }
        }
        dispatcher.dispatch(TestEvent(42))
        val after = usedMemory()
        return after - before
    }

    @Benchmark
    fun memoryUsageLambdaEvents(): Long {
        val before = usedMemory()
        val manager = EventManager(ByteBuddyGenerator())
        repeat(10_000) {
            manager.register(object {
                @EventHandler
                fun onEvent(e: TestEvent) { e.value + 1 }
            })
        }
        manager.call(TestEvent(42))
        val after = usedMemory()
        return after - before
    }

    private fun usedMemory(): Long {
        val bean = ManagementFactory.getMemoryMXBean()
        val heap = bean.heapMemoryUsage
        return heap.used
    }
}
